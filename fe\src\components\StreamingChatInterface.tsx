"use client";

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { Send, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { StreamingMessage, TypingIndicator } from '@/components/ui/streaming-message';
import { RealTimeBalance } from '@/components/ui/real-time-balance';
import { ConnectionStatus } from '@/components/ui/connection-status';
import { useStreamingChat } from '@/hooks/useStreamingChat';
import { useSocket } from '@/contexts/SocketContext';
import { asset } from '@/lib/utils';

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface StreamingChatInterfaceProps {
  expert: Expert | null;
  sessionId?: string;
  initialMessages?: any[];
}

export const StreamingChatInterface: React.FC<StreamingChatInterfaceProps> = ({
  expert,
  sessionId,
  initialMessages = []
}) => {
  const [input, setInput] = useState('');
  
  const chatRef = useRef<HTMLDivElement>(null);
  const { isConnected, connectionError, reconnect } = useSocket();

  const {
    messages,
    isStreaming,
    isTyping,
    error,
    currentCost,
    totalTokens,
    balanceUpdate,
    sendMessage,
    clearError,
    addMessage
  } = useStreamingChat({
    expertId: expert?.id.toString() || '',
    sessionId,
    onMessageComplete: (message) => {
      console.log('✅ Message completed:', message);
    },
    onCostUpdate: (cost, tokens) => {
      console.log('💰 Cost updated:', { cost, tokens });
    },
    onBalanceUpdate: (balance) => {
      console.log('💳 Balance updated:', balance);
    },
    onError: (error) => {
      console.error('❌ Chat error:', error);
    }
  });

  // Load initial messages (only once)
  const initialMessagesLoadedRef = useRef(false);
  useEffect(() => {
    if (initialMessages.length > 0 && !initialMessagesLoadedRef.current) {
      console.log('📥 Loading initial messages:', initialMessages.length);
      initialMessages.forEach(msg => {
        addMessage({
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.timestamp || msg.created_at).getTime()
        });
      });
      initialMessagesLoadedRef.current = true;
    }
  }, [initialMessages, addMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTo({
        top: chatRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages, isTyping]);

  const getExpertIcon = (labels: string[]) => {
    if (!labels) return "🤖";
    if (labels.includes("business") || labels.includes("marketing")) return "💼";
    if (labels.includes("code") || labels.includes("programming")) return "💻";
    if (labels.includes("creative") || labels.includes("design")) return "🎨";
    if (labels.includes("education") || labels.includes("learning")) return "📚";
    if (labels.includes("health") || labels.includes("medical")) return "🏥";
    if (labels.includes("finance") || labels.includes("money")) return "💰";
    return "🤖";
  };

  const handleSend = async () => {
    if (!input.trim() || isStreaming) return;
    
    // Check if expert is loaded
    if (!expert || !expert.id) {
      console.warn('⚠️ Cannot send message: expert not loaded');
      alert('Please wait for the expert to load before sending messages.');
      return;
    }
    
    const messageText = input;
    setInput('');
    
    if (isConnected) {
      // Use streaming chat
      console.log('📤 Sending message with expert:', { expertId: expert?.id, message: messageText.substring(0, 50) + '...' });
      sendMessage(messageText);
    } else {
      // Fallback to regular API
      console.log('🔄 Using fallback API for message:', messageText);
      // You could implement a fallback to the regular chat API here
      // For now, we'll just show that streaming is not available
      alert('Real-time chat is not available. Please log in or refresh the page to enable streaming features.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Link
              href={expert ? `/expert/${expert.id}` : "/"}
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>{expert ? "Back to Profile" : "Back to Home"}</span>
            </Link>

            {expert && (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Chatting with</p>
                  <p className="font-semibold text-gray-900">{expert.name}</p>
                </div>
                {expert.imageUrl ? (
                  <Image
                    src={asset(expert.imageUrl)}
                    alt={expert.name}
                    width={40}
                    height={40}
                    className="w-10 h-10 object-cover rounded-full border-2 border-gray-200"
                  />
                ) : (
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    {getExpertIcon(expert.labels)}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
              <div className="p-6">
                {/* Expert Info Banner */}
                {expert && (
                  <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div className="flex items-center space-x-4">
                      {expert.imageUrl ? (
                        <Image
                          src={asset(expert.imageUrl)}
                          alt={expert.name}
                          width={48}
                          height={48}
                          className="w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm"
                        />
                      ) : (
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm"
                          style={{ backgroundColor: "#1E3A8A" }}
                        >
                          {getExpertIcon(expert.labels)}
                        </div>
                      )}
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{expert.name}</h3>
                        <p className="text-sm text-gray-600">{expert.description}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                            {expert.model}
                          </span>
                          <span className={`text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                            {isConnected ? '● Online' : '● Offline'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Connection Status */}
                <div className="mb-4">
                  <ConnectionStatus 
                    isConnected={isConnected}
                    connectionError={connectionError}
                    onReconnect={reconnect}
                  />
                  {connectionError && connectionError.includes('log in') && (
                    <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-700">
                        💡 <strong>Tip:</strong> You can still use the regular chat without real-time features. 
                        <Link href="/login" className="underline ml-1">Log in</Link> to enable streaming chat.
                      </p>
                    </div>
                  )}
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-red-600">{error}</span>
                      <button
                        onClick={clearError}
                        className="text-red-600 hover:text-red-800 text-sm underline"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                )}

                {/* Chat Messages */}
                <div
                  ref={chatRef}
                  className="h-[60vh] overflow-y-auto space-y-4 mb-6 px-2"
                  style={{ scrollbarWidth: "thin" }}
                >
                  {messages.length === 0 && expert && (
                    <div className="text-center mt-20">
                      <div className="text-6xl mb-4">💬</div>
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">
                        Start a Conversation
                      </h3>
                      <p className="text-gray-500">
                        Hello! I'm {expert.name}. {expert.description} How can I assist you today?
                      </p>
                    </div>
                  )}

                  {messages.filter(message => message && message.id).map((message) => (
                    <StreamingMessage
                      key={message.id}
                      role={message.role}
                      content={message.content || ''}
                      isStreaming={message.isStreaming}
                      timestamp={message.timestamp}
                      cost={message.cost}
                      tokens={message.tokens}
                      expertName={expert?.name}
                      expertImageUrl={expert?.imageUrl}
                      expertIcon={expert ? getExpertIcon(expert.labels) : '🤖'}
                    />
                  ))}

                  {/* Typing Indicator */}
                  {isTyping && (
                    <TypingIndicator
                      expertName={expert?.name}
                      expertImageUrl={expert?.imageUrl}
                      expertIcon={expert ? getExpertIcon(expert.labels) : '🤖'}
                    />
                  )}
                </div>

                {/* Input Area */}
                <form
                  className="flex gap-3 items-end"
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSend();
                  }}
                >
                  <div className="flex-1">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder={
                        expert
                          ? `Ask ${expert.name} anything...`
                          : "Type your message..."
                      }
                      disabled={isStreaming || !isConnected}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          handleSend();
                        }
                      }}
                    />
                  </div>

                  {/* Send Button */}
                  <Button
                    type="submit"
                    disabled={isStreaming || !input.trim() || !isConnected}
                    className="px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    <Send className="w-5 h-5" />
                  </Button>
                </form>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Real-time Balance */}
            <RealTimeBalance
              currentCost={currentCost}
              totalTokens={totalTokens}
              balanceUpdate={balanceUpdate}
              isStreaming={isStreaming}
            />

            {/* Session Info */}
            {expert && (
              <Card className="p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Session Info</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expert:</span>
                    <span className="font-medium">{expert.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span className="font-medium">{expert.model}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Messages:</span>
                    <span className="font-medium">{messages.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`font-medium ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                      {isConnected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};