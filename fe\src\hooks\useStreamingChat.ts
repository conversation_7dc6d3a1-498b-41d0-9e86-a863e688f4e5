"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSocket } from '@/contexts/SocketContext';

interface StreamingMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isStreaming?: boolean;
  cost?: number;
  tokens?: number;
}

interface StreamingChatState {
  messages: StreamingMessage[];
  isStreaming: boolean;
  isTyping: boolean;
  currentStreamId: string | null;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  error: string | null;
  currentCost: number;
  totalTokens: number;
  balanceUpdate: any | null;
}

interface UseStreamingChatOptions {
  expertId: string;
  sessionId?: string;
  onMessageComplete?: (message: StreamingMessage) => void;
  onCostUpdate?: (cost: number, tokens: number) => void;
  onBalanceUpdate?: (balance: any) => void;
  onError?: (error: string) => void;
}

export const useStreamingChat = (options: UseStreamingChatOptions) => {
  const { socket, isConnected, joinChat, leaveChat, sendChatMessage } = useSocket();
  const { expertId, sessionId, onMessageComplete, onCostUpdate, onBalanceUpdate, onError } = options;
  
  // Track if we've ever had a valid expertId
  const hasValidExpertId = expertId && expertId.trim() !== '';

  const [state, setState] = useState<StreamingChatState>({
    messages: [],
    isStreaming: false,
    isTyping: false,
    currentStreamId: null,
    connectionStatus: 'disconnected',
    error: null,
    currentCost: 0,
    totalTokens: 0,
    balanceUpdate: null,
  });

  const currentStreamingMessage = useRef<StreamingMessage | null>(null);
  const messageIdCounter = useRef(0);

  // Generate unique message ID
  const generateMessageId = () => {
    messageIdCounter.current += 1;
    return `msg_${Date.now()}_${messageIdCounter.current}`;
  };

  // Update connection status
  useEffect(() => {
    setState(prev => ({
      ...prev,
      connectionStatus: isConnected ? 'connected' : 'disconnected'
    }));
  }, [isConnected]);

  // Join chat room when connected
  useEffect(() => {
    if (isConnected && hasValidExpertId) {
      console.log('🏠 Auto-joining chat for expert:', expertId);
      joinChat(expertId, sessionId);
    }

    return () => {
      if (isConnected) {
        leaveChat();
      }
    };
  }, [isConnected, hasValidExpertId, expertId, sessionId, joinChat, leaveChat]);

  // Socket event handlers
  useEffect(() => {
    if (!socket) return;

    // Stream started
    const handleStreamStarted = (data: any) => {
      console.log('🚀 Stream started:', data);
      setState(prev => ({
        ...prev,
        isStreaming: true,
        currentStreamId: data.streamId,
        error: null
      }));

      // Create new streaming message
      currentStreamingMessage.current = {
        id: generateMessageId(),
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, currentStreamingMessage.current!]
      }));
    };

    // Stream chunk received
    const handleStreamChunk = (data: any) => {
      console.log('📦 Stream chunk:', data);
      
      if (currentStreamingMessage.current && data.streamId === state.currentStreamId && data.content) {
        currentStreamingMessage.current.content += data.content;
        
        setState(prev => ({
          ...prev,
          messages: prev.messages.map(msg => 
            msg && msg.id === currentStreamingMessage.current?.id 
              ? { ...currentStreamingMessage.current! }
              : msg
          ).filter(msg => msg !== null)
        }));
      }
    };

    // Cost update
    const handleCostUpdate = (data: any) => {
      console.log('💰 Cost update:', data);
      setState(prev => ({
        ...prev,
        currentCost: data.estimatedCost,
        totalTokens: data.totalTokens
      }));

      if (onCostUpdate) {
        onCostUpdate(data.estimatedCost, data.totalTokens);
      }
    };

    // Stream complete
    const handleStreamComplete = (data: any) => {
      console.log('✅ Stream complete:', data);
      
      if (currentStreamingMessage.current) {
        currentStreamingMessage.current.isStreaming = false;
        currentStreamingMessage.current.cost = data.cost;
        currentStreamingMessage.current.tokens = data.totalTokens;

        setState(prev => ({
          ...prev,
          messages: prev.messages.map(msg => 
            msg && msg.id === currentStreamingMessage.current?.id 
              ? { ...currentStreamingMessage.current! }
              : msg
          ).filter(msg => msg !== null),
          isStreaming: false,
          currentStreamId: null,
          currentCost: data.cost,
          totalTokens: data.totalTokens,
          balanceUpdate: data.balanceUsage
        }));

        if (onMessageComplete) {
          onMessageComplete(currentStreamingMessage.current);
        }

        if (onBalanceUpdate && data.balanceUsage) {
          onBalanceUpdate(data.balanceUsage);
        }

        currentStreamingMessage.current = null;
      }
    };

    // Stream error
    const handleStreamError = (data: any) => {
      console.error('❌ Stream error:', data);
      setState(prev => ({
        ...prev,
        isStreaming: false,
        currentStreamId: null,
        error: data.error
      }));

      if (onError) {
        onError(data.error);
      }

      // Clean up streaming message
      if (currentStreamingMessage.current) {
        setState(prev => ({
          ...prev,
          messages: prev.messages.filter(msg => msg && msg.id !== currentStreamingMessage.current?.id)
        }));
        currentStreamingMessage.current = null;
      }
    };

    // Typing indicators
    const handleTypingStart = (data: any) => {
      console.log('⌨️ Typing started:', data);
      setState(prev => ({ ...prev, isTyping: true }));
    };

    const handleTypingStop = (data: any) => {
      console.log('⌨️ Typing stopped:', data);
      setState(prev => ({ ...prev, isTyping: false }));
    };

    // Register event listeners
    socket.on('stream_started', handleStreamStarted);
    socket.on('stream_chunk', handleStreamChunk);
    socket.on('cost_update', handleCostUpdate);
    socket.on('stream_complete', handleStreamComplete);
    socket.on('stream_error', handleStreamError);
    socket.on('typing_start', handleTypingStart);
    socket.on('typing_stop', handleTypingStop);

    // Cleanup
    return () => {
      socket.off('stream_started', handleStreamStarted);
      socket.off('stream_chunk', handleStreamChunk);
      socket.off('cost_update', handleCostUpdate);
      socket.off('stream_complete', handleStreamComplete);
      socket.off('stream_error', handleStreamError);
      socket.off('typing_start', handleTypingStart);
      socket.off('typing_stop', handleTypingStop);
    };
  }, [socket, state.currentStreamId, onMessageComplete, onCostUpdate, onBalanceUpdate, onError]);

  // Send text message
  const sendMessage = useCallback((message: string) => {
    if (!message.trim() || state.isStreaming) return;

    // Validate expertId
    if (!hasValidExpertId) {
      console.error('❌ Cannot send message: expertId is required');
      setState(prev => ({
        ...prev,
        error: 'Expert not loaded. Please wait and try again.'
      }));
      return;
    }

    // Add user message immediately
    const userMessage: StreamingMessage = {
      id: generateMessageId(),
      role: 'user',
      content: message.trim(),
      timestamp: Date.now()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      error: null
    }));

    // Send via socket
    console.log('📤 Sending message via socket:', { expertId, message: message.substring(0, 50) + '...', sessionId });
    sendChatMessage(message.trim(), expertId, sessionId);
  }, [hasValidExpertId, expertId, sessionId, sendChatMessage, state.isStreaming]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Add message manually (for loading existing chat history)
  const addMessage = useCallback((message: Omit<StreamingMessage, 'id'>) => {
    if (!message || !message.content) {
      console.warn('⚠️ Attempted to add null or empty message:', message);
      return;
    }

    const newMessage: StreamingMessage = {
      ...message,
      id: generateMessageId(),
      content: message.content || ''
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages.filter(msg => msg !== null), newMessage]
    }));
  }, []);

  // Clear all messages
  const clearMessages = useCallback(() => {
    setState(prev => ({
      ...prev,
      messages: [],
      currentCost: 0,
      totalTokens: 0,
      balanceUpdate: null
    }));
  }, []);

  return {
    ...state,
    sendMessage,
    clearError,
    addMessage,
    clearMessages,
  };
};