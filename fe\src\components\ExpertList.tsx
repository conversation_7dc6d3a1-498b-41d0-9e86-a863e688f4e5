"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { api } from "@/lib/api";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Eye,
  Edit,
  Users,
  DollarSign,
  TrendingUp,
  FileText,
  Calendar,
  Loader2,
  Plus,
  BarChart3,
  Target,
  Clock,
} from "lucide-react";
import Link from "next/link";
import { asset } from "@/lib/utils";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface ExpertStats {
  totalUsers: number;
  totalCommission: number;
  last30DaysCommission: number;
  totalSessions: number;
  totalMessages: number;
}

interface ExpertListProps {
  onExpertSelect?: (expert: Expert) => void;
  onExpertEdit?: (expert: Expert) => void;
  refreshTrigger?: number;
  onCreateExpert?: () => void;
}

const ExpertList: React.FC<ExpertListProps> = ({
  onExpertEdit,
  refreshTrigger,
  onCreateExpert,
}) => {
  const [experts, setExperts] = useState<Expert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expertStats, setExpertStats] = useState<Record<number, ExpertStats>>(
    {}
  );
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [selectedExpertReport, setSelectedExpertReport] =
    useState<Expert | null>(null);

  const loadExperts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await api.listExperts();

      if (result.success) {
        setExperts(result.experts);
        // Load stats for each expert
        await loadExpertStats(result.experts);
      } else {
        setError(result.error || "Failed to load experts");
      }
    } catch (err: any) {
      setError(err.message || "Failed to load experts");
    } finally {
      setIsLoading(false);
    }
  };

  const loadExpertStats = async (expertList: Expert[]) => {
    const statsMap: Record<number, ExpertStats> = {};

    // Load real stats for each expert
    await Promise.all(
      expertList.map(async (expert) => {
        try {
          const result = await api.getExpertStats(expert.id.toString());
          if (result.success) {
            statsMap[expert.id] = {
              totalUsers: result.stats.totalUsers,
              totalCommission: result.stats.totalCommission,
              last30DaysCommission: result.stats.last30DaysCommission,
              totalSessions: result.stats.totalSessions,
              totalMessages: result.stats.totalMessages,
            };
          } else {
            // Fallback to simulated data if API fails
            statsMap[expert.id] = {
              totalUsers: 0,
              totalCommission: 0,
              last30DaysCommission: 0,
              totalSessions: 0,
              totalMessages: 0,
            };
          }
        } catch (error) {
          console.error(`Failed to load stats for expert ${expert.id}:`, error);
          // Fallback to simulated data
          statsMap[expert.id] = {
            totalUsers: 0,
            totalCommission: 0,
            last30DaysCommission: 0,
            totalSessions: 0,
            totalMessages: 0,
          };
        }
      })
    );

    setExpertStats(statsMap);
  };

  useEffect(() => {
    loadExperts();
  }, [refreshTrigger]); // eslint-disable-line react-hooks/exhaustive-deps

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getExpertIcon = (labels: string[]) => {
    if (!labels) return "🤖";
    if (labels.includes("business") || labels.includes("marketing"))
      return "💼";
    if (labels.includes("code") || labels.includes("programming")) return "💻";
    if (labels.includes("creative") || labels.includes("design")) return "🎨";
    if (labels.includes("education") || labels.includes("learning"))
      return "📚";
    if (labels.includes("health") || labels.includes("medical")) return "🏥";
    if (labels.includes("finance") || labels.includes("money")) return "💰";
    return "🤖";
  };

  const showCommissionReport = (expertId: number) => {
    const expert = experts.find((e) => e.id === expertId);
    if (expert) {
      setSelectedExpertReport(expert);
      setIsReportModalOpen(true);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-12">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-3 text-gray-600">Loading experts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 border-red-200 bg-red-50">
        <div className="text-red-700">
          <p className="font-semibold mb-2">Error</p>
          <p className="text-sm mb-4">{error}</p>
          <Button
            onClick={loadExperts}
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            Retry
          </Button>
        </div>
      </Card>
    );
  }

  if (experts.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-2xl font-bold text-gray-900">
            Your AI Experts (0)
          </h3>
          {onCreateExpert && (
            <Button
              onClick={onCreateExpert}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              Create Expert
            </Button>
          )}
        </div>

        <Card className="p-12 text-center border-dashed border-2 border-gray-300">
          <div className="text-gray-500">
            <div className="text-6xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold mb-2">
              No experts created yet
            </h3>
            <p className="text-sm">
              Create your first AI expert to get started!
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold text-gray-900">
          Your AI Experts ({experts.length})
        </h3>
        {onCreateExpert && (
          <Button
            onClick={onCreateExpert}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center gap-2"
          >
            <Plus className="w-5 h-5" />
            Create Expert
          </Button>
        )}
      </div>

      <div className="grid gap-6">
        {experts.map((expert) => {
          const stats = expertStats[expert.id];

          return (
            <Card
              key={expert.id}
              className="bg-white shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-200"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    {expert.imageUrl ? (
                      <Image
                        src={asset(expert.imageUrl)}
                        alt={expert.name}
                        width={64}
                        height={64}
                        className="w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"
                      />
                    ) : (
                      <div
                        className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg"
                        style={{ backgroundColor: "#1E3A8A" }}
                      >
                        {getExpertIcon(expert.labels)}
                      </div>
                    )}

                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-xl font-bold text-gray-900">
                          {expert.name}
                        </h4>
                        <span className="text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                          {expert.model}
                        </span>
                        {expert.isPublic && (
                          <span className="text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                            Public
                          </span>
                        )}
                      </div>
                      {expert.description && (
                        <p className="text-gray-600 text-sm mb-2">
                          {expert.description}
                        </p>
                      )}

                      {/* Labels */}
                      {expert.labels && expert.labels.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {expert.labels.map((label, index) => (
                            <span
                              key={index}
                              className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md"
                            >
                              {label}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => onExpertEdit?.(expert)}
                      variant="outline"
                      size="sm"
                      className="border-orange-200 text-orange-700 hover:bg-orange-50"
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                    <Link href={`/expert/${expert.id}`}>
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                    </Link>
                  </div>
                </div>

                {/* Statistics */}
                {stats && (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4">
                    <h5 className="font-semibold text-gray-900 mb-3 flex items-center">
                      <TrendingUp className="w-4 h-4 mr-2 text-blue-600" />
                      Performance Stats
                    </h5>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Users className="w-4 h-4 text-blue-600 mr-1" />
                          <span className="text-2xl font-bold text-blue-600">
                            {stats.totalUsers}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">Total Users</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <DollarSign className="w-4 h-4 text-green-600 mr-1" />
                          <span className="text-2xl font-bold text-green-600">
                            {formatCurrency(stats.totalCommission)
                              .replace("IDR", "")
                              .trim()}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          Total Commission
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center mb-1">
                          <Calendar className="w-4 h-4 text-orange-600 mr-1" />
                          <span className="text-2xl font-bold text-orange-600">
                            {formatCurrency(stats.last30DaysCommission)
                              .replace("IDR", "")
                              .trim()}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600">
                          Last 30 Days
                        </div>
                      </div>

                      <div className="text-center">
                        <Button
                          onClick={() => showCommissionReport(expert.id)}
                          size="sm"
                          variant="outline"
                          className="w-full border-purple-200 text-purple-700 hover:bg-purple-50"
                        >
                          <FileText className="w-4 h-4 mr-1" />
                          Report
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Additional Info */}
                <div className="text-xs text-gray-500 space-y-1 border-t border-gray-100 pt-4">
                  <div className="flex justify-between">
                    <span>Pricing: {expert.pricingPercentage}% commission</span>
                    <span>Created: {formatDate(expert.createdAt)}</span>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Commission Report Modal */}
      <Dialog open={isReportModalOpen} onOpenChange={setIsReportModalOpen}>
        <DialogContent className="md:max-w-4xl w-screen max-w-[calc(100%-2rem)] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-xl">
              <BarChart3 className="w-6 h-6 text-blue-600" />
              Commission Report - {selectedExpertReport?.name}
            </DialogTitle>
            <DialogDescription>
              Detailed commission and performance analytics for your AI expert
            </DialogDescription>
          </DialogHeader>

          {selectedExpertReport && (
            <div className="space-y-6 mt-6">
              {/* Expert Overview */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                <div className="flex items-center gap-4 mb-4">
                  {selectedExpertReport.imageUrl ? (
                    <Image
                      src={asset(selectedExpertReport.imageUrl)}
                      alt={selectedExpertReport.name}
                      width={64}
                      height={64}
                      className="w-16 h-16 object-cover rounded-full border-3 border-white shadow-lg"
                    />
                  ) : (
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl shadow-lg"
                      style={{ backgroundColor: "#1E3A8A" }}
                    >
                      {getExpertIcon(selectedExpertReport.labels)}
                    </div>
                  )}
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {selectedExpertReport.name}
                    </h3>
                    <p className="text-gray-600">
                      {selectedExpertReport.description}
                    </p>
                    <div className="flex gap-2 mt-2">
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {selectedExpertReport.model}
                      </span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        {selectedExpertReport.pricingPercentage}% Commission
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Key Metrics */}
              {expertStats[selectedExpertReport.id] && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="p-6 border-green-200 bg-green-50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-green-800">
                        Total Commission
                      </h4>
                      <DollarSign className="w-6 h-6 text-green-600" />
                    </div>
                    <p className="text-3xl font-bold text-green-700">
                      {formatCurrency(
                        expertStats[selectedExpertReport.id].totalCommission
                      )}
                    </p>
                    <p className="text-sm text-green-600 mt-1">
                      All time earnings
                    </p>
                  </Card>

                  <Card className="p-6 border-orange-200 bg-orange-50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-orange-800">
                        Last 30 Days
                      </h4>
                      <Calendar className="w-6 h-6 text-orange-600" />
                    </div>
                    <p className="text-3xl font-bold text-orange-700">
                      {formatCurrency(
                        expertStats[selectedExpertReport.id]
                          .last30DaysCommission
                      )}
                    </p>
                    <p className="text-sm text-orange-600 mt-1">
                      Recent performance
                    </p>
                  </Card>

                  <Card className="p-6 border-blue-200 bg-blue-50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-blue-800">
                        Active Users
                      </h4>
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <p className="text-3xl font-bold text-blue-700">
                      {expertStats[selectedExpertReport.id].totalUsers}
                    </p>
                    <p className="text-sm text-blue-600 mt-1">
                      Total users served
                    </p>
                  </Card>
                </div>
              )}

              {/* Performance Details */}
              <Card className="p-6">
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Target className="w-5 h-5 text-purple-600" />
                  Performance Breakdown
                </h4>

                {expertStats[selectedExpertReport.id] && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-700 font-medium">
                          Total Sessions
                        </span>
                        <span className="text-xl font-bold text-gray-900">
                          {expertStats[selectedExpertReport.id].totalSessions}
                        </span>
                      </div>

                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-700 font-medium">
                          Total Messages
                        </span>
                        <span className="text-xl font-bold text-gray-900">
                          {expertStats[selectedExpertReport.id].totalMessages}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-700 font-medium">
                          Avg. Messages/Session
                        </span>
                        <span className="text-xl font-bold text-gray-900">
                          {expertStats[selectedExpertReport.id].totalSessions >
                          0
                            ? Math.round(
                                expertStats[selectedExpertReport.id]
                                  .totalMessages /
                                  expertStats[selectedExpertReport.id]
                                    .totalSessions
                              )
                            : 0}
                        </span>
                      </div>

                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-700 font-medium">
                          Commission Rate
                        </span>
                        <span className="text-xl font-bold text-gray-900">
                          {selectedExpertReport.pricingPercentage}%
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </Card>

              {/* Expert Details */}
              <Card className="p-6">
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Clock className="w-5 h-5 text-indigo-600" />
                  Expert Information
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Created Date
                      </label>
                      <p className="text-gray-900">
                        {formatDate(selectedExpertReport.createdAt)}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Model
                      </label>
                      <p className="text-gray-900">
                        {selectedExpertReport.model}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Visibility
                      </label>
                      <p className="text-gray-900">
                        {selectedExpertReport.isPublic ? "Public" : "Private"}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {selectedExpertReport.labels &&
                      selectedExpertReport.labels.length > 0 && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">
                            Labels
                          </label>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedExpertReport.labels.map((label, index) => (
                              <span
                                key={index}
                                className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md"
                              >
                                {label}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Last Updated
                      </label>
                      <p className="text-gray-900">
                        {formatDate(selectedExpertReport.updatedAt)}
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setIsReportModalOpen(false)}
                  className="px-6"
                >
                  Close
                </Button>

                <Button
                  onClick={() => {
                    setIsReportModalOpen(false);
                    onExpertEdit?.(selectedExpertReport);
                  }}
                  className="px-6 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Expert
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExpertList;
