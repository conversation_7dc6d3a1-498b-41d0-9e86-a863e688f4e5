"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { api } from "@/lib/api";
import { StreamingChatInterface } from "@/components/StreamingChatInterface";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

function ChatComponent() {
  const searchParams = useSearchParams();
  const expertId = searchParams.get("expertId");
  const threadId = searchParams.get("threadId");

  const [expert, setExpert] = useState<Expert | null>(null);
  const [initialMessages, setInitialMessages] = useState<any[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoadingExpert, setIsLoadingExpert] = useState(!!expertId);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (expertId) {
      console.log("🚀 Initializing chat with expertId:", expertId);
      initializeChatWithExpert();
    } else if (threadId) {
      console.log("🚀 Loading chat with threadId:", threadId);
      loadChatHistory();
    } else {
      console.log("⚠️ No expertId or threadId provided");
      setError("No expert or thread specified");
    }
  }, [expertId, threadId]); // eslint-disable-line react-hooks/exhaustive-deps

  const initializeChatWithExpert = async () => {
    try {
      console.log("📞 Loading expert data for ID:", expertId);
      setIsLoadingExpert(true);
      setIsLoadingHistory(true);
      setError(null);

      // First load expert information
      console.log("📞 About to call api.getExpert with:", expertId);
      const expertResult = await api.getExpert(expertId!);
      console.log("📞 Expert API response:", expertResult);
      
      if (!expertResult.success) {
        console.error("❌ Failed to load expert:", expertResult);
        setError(`Failed to load expert: ${expertResult.error || 'Unknown error'}`);
        return;
      }

      console.log("📞 Setting expert data:", expertResult.expert);
      setExpert(expertResult.expert);
      console.log("✅ Expert loaded successfully:", expertResult.expert.name);

      // Check for existing active session for this expert
      console.log("📞 Checking for active session for expert:", expertId);
      const sessionResult = await api.getActiveSessionForExpert(expertId!);
      console.log("📞 Session API response:", sessionResult);

      if (sessionResult.success && sessionResult.session) {
        // Found existing session - load its history using session ID
        const existingSession = sessionResult.session;
        setCurrentSessionId(existingSession.id.toString());

        console.log("📞 Loading chat history for session:", existingSession.id);
        // Load chat history for this session using the more reliable method
        await loadChatHistoryBySessionId(existingSession.id);

        console.log(
          "✅ Loaded existing session:",
          existingSession.id,
          "with thread:",
          existingSession.thread_id
        );
      } else {
        // No existing session - show welcome message for new chat
        console.log("🆕 No existing session found, starting fresh chat");
        setInitialMessages([
          {
            role: "assistant",
            content: `Hello! I'm ${expertResult.expert.name}. ${expertResult.expert.description} How can I assist you today?`,
            timestamp: Date.now()
          },
        ]);
      }
    } catch (error) {
      console.error("💥 Failed to initialize chat with expert:", error);
      console.error("💥 Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        expertId: expertId
      });
      setError(`Failed to initialize chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingExpert(false);
      setIsLoadingHistory(false);
    }
  };

  const loadChatHistory = async () => {
    if (!threadId) return;
    console.log("📞 Loading chat history for thread:", threadId);
    await loadChatHistoryForThread(threadId);
  };

  const loadChatHistoryForThread = async (targetThreadId: string) => {
    try {
      setIsLoadingHistory(true);
      setError(null);
      console.log("📞 Loading messages for thread:", targetThreadId);
      
      // First, try to get session info from the thread to get expert data
      console.log("📞 Trying to get session info for thread:", targetThreadId);
      try {
        const sessionsResult = await api.getUserChatSessions(50);
        if (sessionsResult.success && sessionsResult.sessions) {
          const sessionWithThread = sessionsResult.sessions.find(
            (session: any) => session.thread_id === targetThreadId
          );
          
          if (sessionWithThread && sessionWithThread.expert_id) {
            console.log("📞 Found session with expert_id:", sessionWithThread.expert_id);
            // Load expert data
            const expertResult = await api.getExpert(sessionWithThread.expert_id.toString());
            if (expertResult.success) {
              setExpert(expertResult.expert);
              setCurrentSessionId(sessionWithThread.id.toString());
              console.log("✅ Expert loaded from session:", expertResult.expert.name);
            }
          }
        }
      } catch (sessionError) {
        console.warn("⚠️ Could not load session info:", sessionError);
      }
      
      const result = await api.getThreadMessages(targetThreadId);
      console.log("📞 Thread messages API response:", result);
      
      if (result.success && result.messages) {
        const formattedMessages = result.messages.map(
          (msg: { role: string; content: string; timestamp?: string; created_at?: string }) => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp || msg.created_at
          })
        );
        setInitialMessages(formattedMessages);
        console.log("✅ Loaded", formattedMessages.length, "messages from thread");

        // Try to get expert info from the thread data
        if (!expert && result.expertId) {
          console.log("📞 Loading expert from thread data, expertId:", result.expertId);
          try {
            const expertResult = await api.getExpert(result.expertId.toString());
            if (expertResult.success) {
              setExpert(expertResult.expert);
              console.log("✅ Expert loaded from thread:", expertResult.expert.name);
            }
          } catch (error) {
            console.warn("⚠️ Could not load expert from thread:", error);
          }
        }
      } else {
        console.error("❌ Failed to load thread messages:", result);
        setError(`Failed to load chat history: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error("💥 Failed to load chat history:", error);
      setError(`Failed to load chat history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const loadChatHistoryBySessionId = async (sessionId: number) => {
    try {
      setIsLoadingHistory(true);
      setError(null);
      console.log("📞 Loading chat history for session:", sessionId);

      const result = await api.getSessionMessages(sessionId, 50);
      console.log("📞 Session messages API response:", result);

      if (result.success && result.messages) {
        const formattedMessages = result.messages.map(
          (msg: { role: string; content: string; timestamp?: string; created_at?: string }) => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp || msg.created_at
          })
        );
        setInitialMessages(formattedMessages);
        console.log(
          "✅ Loaded",
          formattedMessages.length,
          "messages from session",
          sessionId
        );
      } else {
        console.error("❌ Failed to load session messages:", result);
        setError(`Failed to load session messages: ${result.error || 'Unknown error'}`);
        // Session-based loading failed, but we'll continue with empty messages
      }
    } catch (error) {
      console.error("💥 Failed to load chat history by session:", error);
      setError(`Failed to load session messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Session-based loading failed due to error, but we'll continue with empty messages
    } finally {
      setIsLoadingHistory(false);
    }
  };



  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">
            Error Loading Chat
          </h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Link
            href="/"
            className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>
      </div>
    );
  }

  if (isLoadingExpert || isLoadingHistory) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
            style={{ borderColor: "#1E3A8A" }}
          ></div>
          <p className="text-gray-600">
            {isLoadingExpert ? `Loading expert (ID: ${expertId})...` : "Loading chat history..."}
          </p>
          <p className="text-sm text-gray-400 mt-2">
            Debug: expertId={expertId}, threadId={threadId}
          </p>
        </div>
      </div>
    );
  }

  console.log("🔍 ChatComponent render:", { 
    expert: expert ? { id: expert.id, name: expert.name } : null, 
    expertId, 
    isLoadingExpert, 
    error 
  });

  return (
    <StreamingChatInterface
      expert={expert}
      sessionId={currentSessionId || undefined}
      initialMessages={initialMessages}
    />
  );
}

export default function ChatPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ChatComponent />
    </Suspense>
  );
}
