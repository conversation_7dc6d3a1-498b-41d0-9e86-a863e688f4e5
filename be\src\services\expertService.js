const { pool } = require('../config/database');
const assistantService = require('./assistantService');
const CurrencyUtils = require('../utils/currencyUtils');

class ExpertService {
  async createExpert(expertData, knowledgeBaseFile = null, imageFile = null) {
    try {
      const { userId, name, description, systemPrompt, model, pricingPercentage, isPublic, labels } = expertData;

      // Create OpenAI assistant
      const assistantResult = await assistantService.processAssistantCreation({
        name,
        instructions: systemPrompt,
        model: model || 'gpt-4o-mini',
        userId
      }, knowledgeBaseFile);

      if (!assistantResult.success) {
        throw new Error('Failed to create OpenAI assistant');
      }

      // Process image file if provided
      let imageUrl = null;
      if (imageFile) {
        // Store relative path to the uploaded image
        imageUrl = `/uploads/${imageFile.filename}`;
      }

      // Save expert to database
      const query = `
        INSERT INTO experts (user_id, name, description, system_prompt, model, assistant_id, image_url, pricing_percentage, is_public, labels)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        userId,
        name,
        description || '',
        systemPrompt,
        model || 'gpt-4o-mini',
        assistantResult.assistant.id,
        imageUrl,
        pricingPercentage || 0.00,
        isPublic || false,
        labels ? JSON.stringify(labels) : null
      ];

      const [result] = await pool.execute(query, values);

      return {
        success: true,
        expert: {
          id: result.insertId,
          userId,
          name,
          description,
          systemPrompt,
          model: model || 'gpt-4o-mini',
          assistantId: assistantResult.assistant.id,
          imageUrl,
          pricingPercentage: pricingPercentage || 0.00,
          isPublic: isPublic || false,
          labels: labels || [],
          createdAt: new Date()
        }
      };
    } catch (error) {
      console.error('Expert creation error:', error);
      throw error;
    }
  }

  async listUserExperts(userId) {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id,
               image_url, pricing_percentage, is_public, labels, created_at, updated_at
        FROM experts
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;

      const [rows] = await pool.execute(query, [userId]);

      return {
        success: true,
        experts: rows.map(row => {
          let labels = [];
          if (row.labels) {
            try {
              labels = JSON.parse(row.labels);
            } catch (error) {
              // Handle invalid JSON - try to convert comma-separated string to array
              if (typeof row.labels === 'string') {
                labels = row.labels.split(',').map(label => label.trim()).filter(label => label);
              }
              console.warn(`Invalid JSON in labels for expert ${row.id}:`, row.labels);
            }
          }
          
          return {
            id: row.id,
            name: row.name,
            description: row.description,
            systemPrompt: row.system_prompt,
            model: row.model,
            assistantId: row.assistant_id,
            imageUrl: row.image_url,
            pricingPercentage: row.pricing_percentage,
            isPublic: row.is_public || false,
            labels: Array.isArray(labels) ? labels : [],
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };
        })
      };
    } catch (error) {
      console.error('List experts error:', error);
      throw error;
    }
  }

  async getPublicExperts() {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id,
               image_url, pricing_percentage, is_public, labels,
               total_chats, total_revenue, average_rating, total_reviews,
               created_at, updated_at
        FROM experts
        WHERE is_public = 1
        ORDER BY created_at DESC
      `;

      const [rows] = await pool.execute(query);

      return {
        success: true,
        experts: rows.map(row => {
          let labels = [];
          if (row.labels) {
            try {
              labels = JSON.parse(row.labels);
            } catch (error) {
              // Handle invalid JSON - try to convert comma-separated string to array
              if (typeof row.labels === 'string') {
                labels = row.labels.split(',').map(label => label.trim()).filter(label => label);
              }
              console.warn(`Invalid JSON in labels for expert ${row.id}:`, row.labels);
            }
          }
          
          return {
            id: row.id,
            name: row.name,
            description: row.description,
            systemPrompt: row.system_prompt,
            model: row.model,
            assistantId: row.assistant_id,
            imageUrl: row.image_url,
            pricingPercentage: row.pricing_percentage,
            isPublic: row.is_public || false,
            labels: Array.isArray(labels) ? labels : [],
            totalChats: row.total_chats || 0,
            totalRevenue: parseFloat(row.total_revenue || 0),
            averageRating: parseFloat(row.average_rating || 0),
            totalReviews: row.total_reviews || 0,
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };
        })
      };
    } catch (error) {
      console.error('Get public experts error:', error);
      throw error;
    }
  }

  async getExpert(expertId, userId) {
    try {
      // Get expert by ID - any user can access any expert
      // is_public only affects whether expert appears in marketplace listings, not access
      const query = `
        SELECT id, user_id, name, description, system_prompt, model, assistant_id,
               image_url, pricing_percentage, is_public, labels, created_at, updated_at
        FROM experts
        WHERE id = ?
      `;

      const [rows] = await pool.execute(query, [expertId]);

      if (rows.length === 0) {
        return {
          success: false,
          error: 'Expert not found'
        };
      }

      const expert = rows[0];
      const isOwner = expert.user_id === userId;

      return {
        success: true,
        expert: {
          id: expert.id,
          name: expert.name,
          description: expert.description,
          systemPrompt: expert.system_prompt,
          model: expert.model,
          assistantId: expert.assistant_id,
          imageUrl: expert.image_url,

          pricingPercentage: expert.pricing_percentage,
          isPublic: expert.is_public || false,
          isOwner: isOwner, // Add ownership info
          labels: (() => {
            if (!expert.labels) return [];
            try {
              const parsed = JSON.parse(expert.labels);
              return Array.isArray(parsed) ? parsed : [];
            } catch (error) {
              // Handle invalid JSON - try to convert comma-separated string to array
              if (typeof expert.labels === 'string') {
                return expert.labels.split(',').map(label => label.trim()).filter(label => label);
              }
              console.warn(`Invalid JSON in labels for expert ${expert.id}:`, expert.labels);
              return [];
            }
          })(),
          createdAt: expert.created_at,
          updatedAt: expert.updated_at
        }
      };
    } catch (error) {
      console.error('Get expert error:', error);
      throw error;
    }
  }

  async updateExpert(expertId, userId, updateData, knowledgeBaseFile = null, imageFile = null) {
    try {
      // First check if expert exists and belongs to user (only owner can update)
      const ownerCheckQuery = `
        SELECT id, name, description, system_prompt, model, assistant_id, 
               image_url, pricing_percentage, is_public, labels, created_at, updated_at
        FROM experts 
        WHERE id = ? AND user_id = ?
      `;

      const [rows] = await pool.execute(ownerCheckQuery, [expertId, userId]);

      if (rows.length === 0) {
        return {
          success: false,
          error: 'Expert not found or you do not have permission to update this expert'
        };
      }

      const expert = rows[0];
      
      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];

      if (updateData.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(updateData.name);
      }

      if (updateData.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(updateData.description);
      }

      if (updateData.systemPrompt !== undefined) {
        updateFields.push('system_prompt = ?');
        updateValues.push(updateData.systemPrompt);
        
        // Update OpenAI assistant instructions if system prompt changed
        try {
          await assistantService.updateAssistantInstructions(expert.assistantId, updateData.systemPrompt);
        } catch (error) {
          console.warn('Failed to update assistant instructions:', error.message);
        }
      }

      if (updateData.model !== undefined) {
        updateFields.push('model = ?');
        updateValues.push(updateData.model);
      }

      if (updateData.pricingPercentage !== undefined) {
        updateFields.push('pricing_percentage = ?');
        updateValues.push(updateData.pricingPercentage);
      }

      if (updateData.isPublic !== undefined) {
        updateFields.push('is_public = ?');
        updateValues.push(updateData.isPublic);
      }



      if (updateData.labels !== undefined) {
        updateFields.push('labels = ?');
        updateValues.push(JSON.stringify(updateData.labels));
      }

      // Process image file if provided
      if (imageFile) {
        const imageUrl = `/uploads/${imageFile.filename}`;
        updateFields.push('image_url = ?');
        updateValues.push(imageUrl);
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      if (updateFields.length === 1) { // Only updated_at was added
        return {
          success: false,
          error: 'No fields to update'
        };
      }

      // Add expertId and userId to values for WHERE clause
      updateValues.push(expertId, userId);

      const query = `
        UPDATE experts 
        SET ${updateFields.join(', ')}
        WHERE id = ? AND user_id = ?
      `;

      const [result] = await pool.execute(query, updateValues);

      if (result.affectedRows === 0) {
        return {
          success: false,
          error: 'Expert not found or no changes made'
        };
      }

      // Handle knowledge base file update if provided
      if (knowledgeBaseFile) {
        try {
          await assistantService.updateAssistantKnowledgeBase(expert.assistantId, knowledgeBaseFile);
        } catch (error) {
          console.warn('Failed to update knowledge base:', error.message);
        }
      }

      // Return updated expert
      return await this.getExpert(expertId, userId);

    } catch (error) {
      console.error('Expert update error:', error);
      throw error;
    }
  }

  validateExpertData(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.systemPrompt || data.systemPrompt.trim() === '') {
      errors.push('System prompt is required');
    }

    if (data.model && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    if (data.pricingPercentage && (isNaN(data.pricingPercentage) || data.pricingPercentage < 0 || data.pricingPercentage > 100)) {
      errors.push('Pricing percentage must be between 0 and 100');
    }

    if (data.labels && Array.isArray(data.labels)) {
      if (data.labels.length > 5) {
        errors.push('Maximum 5 labels allowed');
      }
      
      for (const label of data.labels) {
        if (typeof label !== 'string' || label.trim() === '') {
          errors.push('All labels must be non-empty strings');
          break;
        }
        if (label.length > 50) {
          errors.push('Each label must be 50 characters or less');
          break;
        }
      }
    }

    return errors;
  }

  validateExpertUpdateData(data) {
    const errors = [];

    if (data.name !== undefined && (!data.name || data.name.trim() === '')) {
      errors.push('Name cannot be empty');
    }

    if (data.systemPrompt !== undefined && (!data.systemPrompt || data.systemPrompt.trim() === '')) {
      errors.push('System prompt cannot be empty');
    }

    if (data.model !== undefined && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    if (data.pricingPercentage !== undefined && (isNaN(data.pricingPercentage) || data.pricingPercentage < 0 || data.pricingPercentage > 100)) {
      errors.push('Pricing percentage must be between 0 and 100');
    }

    if (data.labels !== undefined && Array.isArray(data.labels)) {
      if (data.labels.length > 5) {
        errors.push('Maximum 5 labels allowed');
      }
      
      for (const label of data.labels) {
        if (typeof label !== 'string' || label.trim() === '') {
          errors.push('All labels must be non-empty strings');
          break;
        }
        if (label.length > 50) {
          errors.push('Each label must be 50 characters or less');
          break;
        }
      }
    }

    return errors;
  }

  getValidModels() {
    return [
      {
        "model": "gpt-3.5-turbo",
        "input": 0.5,
        "output": 1.5
      },
      {
        "model": "gpt-4",
        "input": 30,
        "output": 60
      },
      {
        "model": "gpt-4-turbo",
        "input": 10,
        "output": 30
      },
      {
        "model": "gpt-4.1",
        "input": 5,
        "output": 15
      },
      {
        "model": "gpt-4.1-mini",
        "input": 0.4,
        "output": 1.6
      },
      {
        "model": "gpt-4.1-nano",
        "input": 0.1,
        "output": 0.1
      },
      {
        "model": "gpt-4o",
        "input": 5,
        "output": 15
      },
      {
        "model": "gpt-4o-mini",
        "input": 0.15,
        "output": 0.6
      },
      {
        "model": "o1",
        "input": 15,
        "output": 75
      },
      {
        "model": "o3-mini",
        "input": 1,
        "output": 2
      }
    ];
  }

  isValidModel(model) {
    const validModels = this.getValidModels();
    return validModels.some(m => m.model === model);
  }

  getModelPricing(model) {
    const validModels = this.getValidModels();
    const modelData = validModels.find(m => m.model === model);
    return modelData ? { input: modelData.input, output: modelData.output } : null;
  }

  calculateTokenCost(model, inputTokens, outputTokens, currency = 'IDR') {
    const pricing = this.getModelPricing(model);
    if (!pricing) {
      console.warn(`Unknown model pricing for: ${model}`);
      return 0;
    }

    // Pricing is per 1M tokens, so we need to divide by 1,000,000
    const inputCost = (inputTokens / 1000000) * pricing.input;
    const outputCost = (outputTokens / 1000000) * pricing.output;
    
    const basePriceUSD = inputCost + outputCost;
    
    // Convert to IDR if requested (default)
    if (currency === 'IDR') {
      return basePriceUSD * 20000; // $1 = Rp 20,000
    }
    
    return basePriceUSD;
  }

  calculateExpertTokenCost(model, inputTokens, outputTokens, pricingPercentage = 0, currency = 'IDR') {
    const pricing = this.getModelPricing(model);
    if (!pricing) {
      console.warn(`Unknown model pricing for: ${model}`);
      return {
        basePrice: 0,
        platformCommission: 0,
        expertCommission: 0,
        totalPrice: 0
      };
    }

    // Calculate base price (OpenAI cost)
    const inputCost = (inputTokens / 1000000) * pricing.input;
    const outputCost = (outputTokens / 1000000) * pricing.output;
    const basePriceUSD = inputCost + outputCost;

    // Calculate commissions
    const platformCommissionUSD = 1 * basePriceUSD; // 100% platform commission
    const expertCommissionUSD = (pricingPercentage / 100) * basePriceUSD; // Expert's percentage
    const totalPriceUSD = basePriceUSD + platformCommissionUSD + expertCommissionUSD;

    // Convert to requested currency
    if (currency === 'IDR') {
      return {
        basePrice: CurrencyUtils.roundIDR(basePriceUSD * 20000),
        platformCommission: CurrencyUtils.roundIDR(platformCommissionUSD * 20000),
        expertCommission: CurrencyUtils.roundIDR(expertCommissionUSD * 20000),
        totalPrice: CurrencyUtils.roundIDR(totalPriceUSD * 20000),
        breakdown: {
          basePriceUSD: CurrencyUtils.roundUSD(basePriceUSD),
          platformCommissionUSD: CurrencyUtils.roundUSD(platformCommissionUSD),
          expertCommissionUSD: CurrencyUtils.roundUSD(expertCommissionUSD),
          totalPriceUSD: CurrencyUtils.roundUSD(totalPriceUSD),
          pricingPercentage: pricingPercentage
        }
      };
    }

    return {
      basePrice: CurrencyUtils.roundUSD(basePriceUSD),
      platformCommission: CurrencyUtils.roundUSD(platformCommissionUSD),
      expertCommission: CurrencyUtils.roundUSD(expertCommissionUSD),
      totalPrice: CurrencyUtils.roundUSD(totalPriceUSD),
      breakdown: {
        basePriceIDR: CurrencyUtils.roundIDR(basePriceUSD * 20000),
        platformCommissionIDR: CurrencyUtils.roundIDR(platformCommissionUSD * 20000),
        expertCommissionIDR: CurrencyUtils.roundIDR(expertCommissionUSD * 20000),
        totalPriceIDR: CurrencyUtils.roundIDR(totalPriceUSD * 20000),
        pricingPercentage: pricingPercentage
      }
    };
  }

  calculateTokenCostInIDR(model, inputTokens, outputTokens) {
    return this.calculateTokenCost(model, inputTokens, outputTokens, 'IDR');
  }

  calculateTokenCostInUSD(model, inputTokens, outputTokens) {
    return this.calculateTokenCost(model, inputTokens, outputTokens, 'USD');
  }

  getAvailableModels() {
    const models = this.getValidModels();
    return {
      success: true,
      models: models.map(m => ({
        name: m.model,
        displayName: this.getModelDisplayName(m.model),
        inputPrice: m.input,
        outputPrice: m.output,
        inputPriceIDR: CurrencyUtils.convertUSDToIDR(m.input),
        outputPriceIDR: CurrencyUtils.convertUSDToIDR(m.output),
        inputPriceFormatted: CurrencyUtils.formatUSD(m.input),
        outputPriceFormatted: CurrencyUtils.formatUSD(m.output),
        inputPriceFormattedIDR: CurrencyUtils.formatIDR(CurrencyUtils.convertUSDToIDR(m.input)),
        outputPriceFormattedIDR: CurrencyUtils.formatIDR(CurrencyUtils.convertUSDToIDR(m.output)),
        description: this.getModelDescription(m.model)
      }))
    };
  }

  getModelDisplayName(model) {
    const displayNames = {
      'gpt-3.5-turbo': 'GPT-3.5 Turbo',
      'gpt-4': 'GPT-4',
      'gpt-4-turbo': 'GPT-4 Turbo',
      'gpt-4.1': 'GPT-4.1',
      'gpt-4.1-mini': 'GPT-4.1 Mini',
      'gpt-4.1-nano': 'GPT-4.1 Nano',
      'gpt-4o': 'GPT-4o',
      'gpt-4o-mini': 'GPT-4o Mini',
      'o1': 'O1',
      'o3-mini': 'O3 Mini'
    };
    return displayNames[model] || model;
  }

  getModelDescription(model) {
    const descriptions = {
      'gpt-3.5-turbo': 'Fast and efficient for most tasks',
      'gpt-4': 'Most capable model for complex tasks',
      'gpt-4-turbo': 'Enhanced version of GPT-4 with better performance',
      'gpt-4.1': 'Latest GPT-4 with improved capabilities',
      'gpt-4.1-mini': 'Compact version of GPT-4.1',
      'gpt-4.1-nano': 'Ultra-lightweight GPT-4.1',
      'gpt-4o': 'Optimized GPT-4 for various tasks',
      'gpt-4o-mini': 'Efficient version of GPT-4o',
      'o1': 'Advanced reasoning model',
      'o3-mini': 'Efficient O3 model for quick responses'
    };
    return descriptions[model] || 'AI language model';
  }

  async getExpertStats(expertId, userId) {
    try {
      // First verify that this expert belongs to the user
      const expertQuery = `
        SELECT id, name FROM experts 
        WHERE id = ? AND user_id = ?
      `;
      
      const [expertRows] = await pool.execute(expertQuery, [expertId, userId]);

      if (expertRows.length === 0) {
        return {
          success: false,
          error: 'Expert not found or access denied'
        };
      }

      // Get expert statistics from chat sessions
      const statsQuery = `
        SELECT 
          COUNT(DISTINCT cs.user_id) as total_users,
          COUNT(DISTINCT cs.id) as total_sessions,
          COUNT(cm.id) as total_messages,
          COALESCE(SUM(cm.cost), 0) as total_revenue,
          COALESCE(SUM(CASE 
            WHEN cm.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
            THEN cm.cost 
            ELSE 0 
          END), 0) as last_30_days_revenue
        FROM chat_sessions cs
        LEFT JOIN chat_messages cm ON cs.id = cm.session_id
        WHERE cs.expert_id = ? AND cs.is_active = TRUE
      `;

      const [statsRows] = await pool.execute(statsQuery, [expertId]);
      const stats = statsRows[0];

      // Calculate expert commission (based on pricingPercentage)
      const expert = expertRows[0];
      const totalRevenue = parseFloat(stats.total_revenue || 0);
      const last30DaysRevenue = parseFloat(stats.last_30_days_revenue || 0);

      // For now, we'll assume the expert gets their percentage of the total cost
      // This would need to be adjusted based on the actual commission calculation logic
      const expertQuery2 = `SELECT pricing_percentage FROM experts WHERE id = ?`;
      const [pricingRows] = await pool.execute(expertQuery2, [expertId]);
      const pricingPercentage = pricingRows[0]?.pricing_percentage || 0;

      const totalCommission = (totalRevenue * pricingPercentage) / 100;
      const last30DaysCommission = (last30DaysRevenue * pricingPercentage) / 100;

      return {
        success: true,
        stats: {
          expertId: parseInt(expertId),
          expertName: expert.name,
          totalUsers: parseInt(stats.total_users || 0),
          totalSessions: parseInt(stats.total_sessions || 0),
          totalMessages: parseInt(stats.total_messages || 0),
          totalRevenue: totalRevenue,
          totalCommission: totalCommission,
          last30DaysRevenue: last30DaysRevenue,
          last30DaysCommission: last30DaysCommission,
          pricingPercentage: pricingPercentage,
          totalCommissionFormatted: CurrencyUtils.formatIDR(totalCommission),
          last30DaysCommissionFormatted: CurrencyUtils.formatIDR(last30DaysCommission)
        }
      };

    } catch (error) {
      console.error('Get expert stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all active experts for synchronization with recommender service
   */
  async getAllActiveExperts() {
    try {
      const query = `
        SELECT 
          e.id,
          e.user_id,
          e.name,
          e.description,
          e.labels,
          e.pricing_percentage as price_per_message,
          e.total_chats,
          e.average_rating,
          e.total_reviews,
          e.created_at,
          e.updated_at,
          e.is_public as is_active,
          u.name as creator_name
        FROM experts e
        LEFT JOIN user u ON e.user_id = u.user_id
        WHERE e.is_public = true
        ORDER BY e.updated_at DESC
      `;

      const [experts] = await pool.execute(query);

      return experts.map(expert => ({
        id: expert.id,
        user_id: expert.user_id,
        name: expert.name,
        description: expert.description,
        labels: expert.labels ? JSON.parse(expert.labels) : [],
        category: this.extractCategoryFromLabels(expert.labels),
        price_per_message: expert.price_per_message || 0,
        total_chats: expert.total_chats || 0,
        average_rating: expert.average_rating || 0,
        total_reviews: expert.total_reviews || 0,
        created_at: expert.created_at,
        updated_at: expert.updated_at,
        is_active: expert.is_active,
        creator_name: expert.creator_name
      }));
    } catch (error) {
      console.error('Error getting all active experts:', error);
      throw error;
    }
  }

  /**
   * Get popular experts for fallback recommendations
   */
  async getPopularExperts(limit = 10) {
    try {
      const query = `
        SELECT 
          e.id,
          e.name,
          e.description,
          e.labels,
          e.pricing_percentage,
          e.total_chats,
          e.average_rating,
          e.total_reviews,
          e.image_url
        FROM experts e
        WHERE e.is_public = true
        ORDER BY 
          (e.total_chats * 0.4 + e.average_rating * e.total_reviews * 0.6) DESC,
          e.total_chats DESC
        LIMIT ?
      `;

      const [experts] = await pool.execute(query, [limit]);
      return experts;
    } catch (error) {
      console.error('Error getting popular experts:', error);
      throw error;
    }
  }

  /**
   * Get expert by ID for recommendation service
   */
  async getExpertById(expertId) {
    try {
      const query = `
        SELECT 
          e.id,
          e.name,
          e.description,
          e.labels,
          e.pricing_percentage,
          e.total_chats,
          e.average_rating,
          e.total_reviews,
          e.is_public
        FROM experts e
        WHERE e.id = ?
      `;

      const [experts] = await pool.execute(query, [expertId]);
      return experts.length > 0 ? experts[0] : null;
    } catch (error) {
      console.error('Error getting expert by ID:', error);
      throw error;
    }
  }

  /**
   * Extract category from labels for recommendation service
   */
  extractCategoryFromLabels(labelsJson) {
    try {
      if (!labelsJson) return 'General';
      
      const labels = JSON.parse(labelsJson);
      if (!Array.isArray(labels) || labels.length === 0) return 'General';

      // Define category mappings
      const categoryMappings = {
        'Technology': ['programming', 'coding', 'tech', 'software', 'development', 'ai', 'machine learning', 'data science'],
        'Business': ['business', 'marketing', 'sales', 'finance', 'entrepreneurship', 'management'],
        'Education': ['education', 'teaching', 'learning', 'academic', 'study', 'research'],
        'Health': ['health', 'medical', 'fitness', 'wellness', 'nutrition', 'mental health'],
        'Creative': ['creative', 'art', 'design', 'writing', 'music', 'photography'],
        'Language': ['language', 'translation', 'english', 'communication', 'linguistics'],
        'Lifestyle': ['lifestyle', 'travel', 'cooking', 'fashion', 'relationships', 'personal development']
      };

      // Find matching category
      for (const [category, keywords] of Object.entries(categoryMappings)) {
        for (const label of labels) {
          if (keywords.some(keyword => label.toLowerCase().includes(keyword.toLowerCase()))) {
            return category;
          }
        }
      }

      return 'General';
    } catch (error) {
      return 'General';
    }
  }
}

module.exports = new ExpertService();