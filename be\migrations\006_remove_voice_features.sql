-- Migration: Remove Voice Features
-- Task 6.3: Database Voice Cleanup
-- 
-- This SQL script removes all voice-related features from the database:
-- - Remove voice_enabled column from experts table
-- - Remove voice-related entries from ai_generation_logs
-- - Remove voice message types from chat_messages
-- - Remove voice_duration column from chat_messages

-- Start transaction
START TRANSACTION;

-- 1. Remove voice-related entries from ai_generation_logs
DELETE FROM ai_generation_logs 
WHERE generation_type IN ('voice_tts', 'voice_stt');

-- 2. Update chat_messages to remove voice message types
UPDATE chat_messages 
SET message_type = 'text' 
WHERE message_type = 'voice';

-- 3. Remove voice_duration column from chat_messages (if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'chat_messages' 
     AND COLUMN_NAME = 'voice_duration') > 0,
    'ALTER TABLE chat_messages DROP COLUMN voice_duration',
    'SELECT "voice_duration column not found" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Update message_type enum to remove 'voice' option
ALTER TABLE chat_messages 
MODIFY COLUMN message_type ENUM('text', 'image') DEFAULT 'text';

-- 5. Remove voice_enabled column from experts table (if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'experts' 
     AND COLUMN_NAME = 'voice_enabled') > 0,
    'ALTER TABLE experts DROP COLUMN voice_enabled',
    'SELECT "voice_enabled column not found" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. Remove voice_enabled index if it exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'experts' 
     AND INDEX_NAME = 'idx_experts_voice_enabled') > 0,
    'DROP INDEX idx_experts_voice_enabled ON experts',
    'SELECT "idx_experts_voice_enabled index not found" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Commit transaction
COMMIT;

-- Display completion message
SELECT 'Voice features cleanup completed successfully!' as result;
