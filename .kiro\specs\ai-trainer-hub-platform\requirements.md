# Requirements Document - AI Trainer Hub Platform

## Introduction

AI Trainer Hub adalah platform marketplace yang memungkinkan pengguna untuk berinteraksi dengan AI experts yang telah dikustomisasi untuk berbagai domain spesifik. Platform ini menggabungkan teknologi OpenAI dengan sistem ekonomi berbasis poin/kredit, sistem afiliasi, dan marketplace yang user-friendly. Pengguna dapat membuat AI experts mereka sendiri, menjualnya di marketplace, atau menggunakan experts yang dibuat oleh pengguna lain.

## Requirements

### Requirement 1: User Authentication & Profile Management

**User Story:** As a user, I want to register and manage my account securely, so that I can access the platform features and maintain my profile information.

#### Acceptance Criteria

1. WHEN a user registers with phone, email, name, and password THEN the system SHALL send an OTP for verification
2. WHEN a user enters a valid OTP THEN the system SHALL activate the account and provide JWT authentication token
3. WHEN a user logs in with valid credentials THEN the system SHALL provide access to authenticated features
4. WHEN a user updates their profile THEN the system SHALL validate and save the changes including banking information
5. WHEN a user requests password reset THEN the system SHALL send OTP and allow secure password change
6. WHEN a user logs out THEN the system SHALL invalidate the authentication token

### Requirement 2: AI Expert Creation & Management

**User Story:** As a content creator, I want to create and customize AI experts with specific knowledge and behavior, so that I can offer specialized AI services to other users.

#### Acceptance Criteria

1. WHEN a user creates an expert THEN the system SHALL require name, description, system prompt, and model selection
2. WHEN an expert is created THEN the system SHALL integrate with OpenAI Assistants API to create the underlying AI assistant
3. WHEN a user uploads knowledge base files THEN the system SHALL process and attach them to the expert's assistant
4. WHEN a user sets pricing percentage THEN the system SHALL validate it's between 0-100% for commission calculation
5. WHEN a user marks an expert as public THEN the system SHALL make it available in the marketplace
6. WHEN a user edits an expert THEN the system SHALL update both database records and OpenAI assistant configuration
7. WHEN a user uploads an expert image THEN the system SHALL store and optimize it for display
8. WHEN a user creates an expert without uploading an image THEN the system SHALL offer to generate an AI image automatically
9. WHEN user chooses AI image generation THEN the system SHALL use GPT-Image-1 model via OpenAI Image API to create an image based on expert name and description
10. WHEN AI image is generated THEN the system SHALL present it to the user for approval before saving
11. WHEN user approves AI-generated image THEN the system SHALL store it as the expert's profile image
12. WHEN user rejects AI-generated image THEN the system SHALL offer to regenerate with different prompts or use default placeholder
13. WHEN AI image generation is requested THEN the system SHALL deduct the generation cost from user's balance before processing
14. WHEN user has insufficient balance for image generation THEN the system SHALL prevent generation and show balance requirement
15. WHEN using DALL-E for image generation THEN the system SHALL configure appropriate quality settings (standard/hd) and size options (1024x1024, 1024x1792, 1792x1024)
16. WHEN generating images THEN the system SHALL use PNG format by default for quality and transparency support
15. WHEN creating an expert THEN the system SHALL provide optional field for "first message" that expert will send to new users
16. WHEN user starts a new chat with an expert that has first message configured THEN the system SHALL display the first message as if sent by the expert
17. WHEN expert has no first message configured THEN the system SHALL start with empty chat waiting for user's first message


### Requirement 2.1: AI-Generated Labels & Tags

**User Story:** As an expert creator, I want the system to automatically generate relevant labels for my expert, so that users can easily discover it through search and filtering.

#### Acceptance Criteria

1. WHEN a user creates an expert THEN the system SHALL analyze the name, description, and system prompt using AI
2. WHEN AI analysis is complete THEN the system SHALL generate 5 relevant labels/hashtags for the expert
3. WHEN labels are generated THEN the system SHALL present them to the creator for review and editing
4. WHEN creator reviews labels THEN the system SHALL allow them to accept, modify, or replace any generated labels
5. WHEN labels are finalized THEN the system SHALL store them and make them searchable in the marketplace
6. WHEN expert is updated THEN the system SHALL offer to regenerate labels based on the new content
7. WHEN generating labels THEN the system SHALL ensure they are relevant, concise, and follow platform guidelines
8. WHEN AI label generation is requested THEN the system SHALL deduct the generation cost from user's balance before processing
9. WHEN user has insufficient balance for label generation THEN the system SHALL prevent generation and allow manual label entry instead

### Requirement 3: Expert Marketplace & Discovery

**User Story:** As a user, I want to browse and discover AI experts in a marketplace, so that I can find specialists that match my specific needs.

#### Acceptance Criteria

1. WHEN a user visits the marketplace THEN the system SHALL display all public experts with their details
2. WHEN a user searches for experts THEN the system SHALL filter results by name, description, and labels
3. WHEN a user filters by model type THEN the system SHALL show only experts using the selected AI model
4. WHEN a user sorts experts THEN the system SHALL order by popularity, name, or other criteria
5. WHEN a user views an expert profile THEN the system SHALL show detailed information, pricing, and statistics
6. WHEN a user clicks on an expert THEN the system SHALL provide option to start a chat session

### Requirement 4: Real-time Chat System

**User Story:** As a user, I want to chat with AI experts in real-time, so that I can get specialized assistance and advice for my specific needs.

#### Acceptance Criteria

1. WHEN a user starts a chat with an expert THEN the system SHALL create a new chat session and OpenAI thread
2. WHEN a user sends a message THEN the system SHALL process it through the expert's AI assistant
3. WHEN the AI responds THEN the system SHALL calculate token costs and deduct from user balance
4. WHEN a chat session is active THEN the system SHALL maintain conversation context and history
5. WHEN a user has insufficient balance THEN the system SHALL prevent message sending and show balance warning
6. WHEN a user views chat history THEN the system SHALL display all previous sessions with timestamps
7. WHEN a user deletes a chat session THEN the system SHALL remove it from their history
8. WHEN AI assistant generates a response THEN the system SHALL stream the response in real-time to provide immediate feedback
9. WHEN streaming response THEN the system SHALL use WebSocket or Server-Sent Events to deliver response chunks as they are generated
10. WHEN backend receives OpenAI assistant stream THEN the system SHALL proxy the stream to the frontend client in real-time
11. WHEN streaming is interrupted THEN the system SHALL handle reconnection and resume streaming from the last received chunk
12. WHEN user sends a message THEN the system SHALL immediately show "AI is typing" indicator while processing


### Requirement 4.1: Expert Sharing & Privacy Controls

**User Story:** As any user, I want to share experts with others and control whether I can monitor their chat sessions, so that I can recommend experts while having appropriate oversight options.

#### Acceptance Criteria

1. WHEN any user shares an expert THEN the system SHALL generate a shareable link with privacy settings
2. WHEN creating a share link THEN the system SHALL provide a checkbox option: "Allow me to monitor visitors' chat sessions with this expert"
3. WHEN a visitor accesses a shared expert link THEN the system SHALL show only the expert details and allow them to start chatting
4. WHEN "monitor visitor chats" is enabled THEN the system SHALL allow the sharer to view chat sessions between visitors and the expert
5. WHEN "monitor visitor chats" is disabled THEN the system SHALL keep visitor chat sessions completely private from the sharer
6. WHEN a visitor chats with a shared expert THEN the system SHALL create their session according to the monitoring privacy setting
7. WHEN the sharing user views their shared link analytics THEN the system SHALL show visitor count and chat activity (if monitoring is enabled)
8. WHEN sharing user changes privacy settings THEN the system SHALL apply changes to future interactions while preserving existing session privacy
9. WHEN expert creator shares their own expert THEN the system SHALL have the same monitoring options as regular users
10. WHEN visitor accesses a shared expert with monitoring enabled THEN the system SHALL display a clear notice before chat starts that their conversations may be viewed by the person who shared the link
11. WHEN visitor sees the monitoring notice THEN the system SHALL require explicit consent (accept/decline) before allowing chat to proceed
12. WHEN visitor declines monitoring consent THEN the system SHALL not allow them to chat with that shared expert
13. WHEN visitor accepts monitoring consent THEN the system SHALL proceed with chat and allow the sharer to view their conversations

### Requirement 5: Balance & Transaction System

**User Story:** As a user, I want to manage my points and credits balance, so that I can pay for AI expert services and track my spending.

#### Acceptance Criteria

1. WHEN a new user registers THEN the system SHALL provide welcome bonus points
2. WHEN a user makes a payment THEN the system SHALL prioritize using points before credits
3. WHEN a user's expert is used THEN the system SHALL calculate and credit commission based on pricing percentage
4. WHEN a transaction occurs THEN the system SHALL record it with timestamp, amount, type, and description
5. WHEN a user checks balance THEN the system SHALL show separate points and credits totals
6. WHEN a user views transaction history THEN the system SHALL display paginated list of all transactions
7. WHEN a user checks affordability THEN the system SHALL verify if they have sufficient balance for a cost

### Requirement 5.1: Platform Commission System

**User Story:** As the platform owner, I want to earn commission from expert usage, so that the platform can generate revenue and remain sustainable.

#### Acceptance Criteria

1. WHEN a user chats with an expert THEN the system SHALL calculate platform commission from the total cost
2. WHEN expert commission is calculated THEN the system SHALL deduct platform commission before crediting expert owner
3. WHEN platform commission rates change THEN the system SHALL apply new rates to future transactions only
4. WHEN commission is collected THEN the system SHALL record it separately from user transactions
5. WHEN admin views platform earnings THEN the system SHALL show total commission collected by time period

### Requirement 6: Affiliate & Referral System

**User Story:** As a user, I want to refer others to the platform and earn commissions, so that I can benefit from growing the user base.

#### Acceptance Criteria

1. WHEN a user generates a referral code THEN the system SHALL create a unique code linked to their account
2. WHEN a visitor clicks a referral link THEN the system SHALL track the visitor with a unique ID
3. WHEN a tracked visitor registers THEN the system SHALL link the registration to the referrer
4. WHEN a referred user makes transactions THEN the system SHALL calculate and credit affiliate commissions
5. WHEN a user views affiliate stats THEN the system SHALL show referral count, earnings, and performance metrics
6. WHEN an affiliate link expires THEN the system SHALL no longer track new visitors from that link

### Requirement 6.1: Affiliate Commission Structure

**User Story:** As an affiliate, I want to earn commissions from referred users' activities, so that I can be rewarded for bringing valuable users to the platform.

#### Acceptance Criteria

1. WHEN a referred user purchases credits THEN the system SHALL credit affiliate with percentage commission
2. WHEN a referred user uses experts THEN the system SHALL credit affiliate with percentage of transaction value
3. WHEN affiliate commission is calculated THEN the system SHALL use predefined commission rates by activity type
4. WHEN affiliate earnings reach minimum threshold THEN the system SHALL allow withdrawal or payout
5. WHEN affiliate commission rates change THEN the system SHALL notify existing affiliates and apply to future earnings
6. WHEN affiliate views earnings breakdown THEN the system SHALL show commission by source (credit purchases, expert usage, etc.)
7. WHEN multiple commission types apply THEN the system SHALL calculate each type separately and sum the total

### Requirement 7: File Upload & Knowledge Base Management

**User Story:** As an expert creator, I want to upload files to enhance my AI expert's knowledge, so that it can provide more accurate and specialized responses.

#### Acceptance Criteria

1. WHEN a user uploads a knowledge base file THEN the system SHALL validate file type and size limits
2. WHEN a file is uploaded THEN the system SHALL process it through OpenAI's file processing system
3. WHEN a file is processed THEN the system SHALL attach it to the expert's assistant for enhanced responses
4. WHEN a user uploads an expert image THEN the system SHALL optimize and store it for marketplace display
5. WHEN file upload fails THEN the system SHALL provide clear error messages and retry options

### Requirement 8: API Documentation & Integration

**User Story:** As a developer, I want comprehensive API documentation, so that I can integrate with the platform or understand its functionality.

#### Acceptance Criteria

1. WHEN a developer accesses API documentation THEN the system SHALL provide interactive Swagger UI
2. WHEN API endpoints are called THEN the system SHALL return consistent JSON responses with proper HTTP status codes
3. WHEN authentication is required THEN the system SHALL validate JWT tokens and return appropriate errors
4. WHEN API errors occur THEN the system SHALL return structured error messages with helpful details
5. WHEN API documentation is updated THEN the system SHALL reflect changes in real-time

### Requirement 9: Performance & Scalability

**User Story:** As a user, I want the platform to be fast and reliable, so that I can have a smooth experience even during high usage periods.

#### Acceptance Criteria

1. WHEN multiple users access the platform THEN the system SHALL handle concurrent requests efficiently
2. WHEN database queries are executed THEN the system SHALL use connection pooling and optimized queries
3. WHEN images are displayed THEN the system SHALL serve optimized versions for different screen sizes
4. WHEN API responses are generated THEN the system SHALL complete within acceptable time limits
5. WHEN errors occur THEN the system SHALL log them appropriately without exposing sensitive information

### Requirement 10: Revenue Distribution & Economic Model

**User Story:** As a stakeholder, I want a clear revenue distribution model, so that all parties (platform, expert creators, affiliates) are fairly compensated.

#### Acceptance Criteria

1. WHEN a user pays for expert usage THEN the system SHALL distribute revenue according to predefined percentages
2. WHEN revenue is distributed THEN the system SHALL allocate portions to: expert creator, platform commission, and affiliate commission (if applicable)
3. WHEN an expert has custom pricing percentage THEN the system SHALL use that percentage for expert commission calculation
4. WHEN no affiliate is involved THEN the system SHALL distribute revenue only between expert creator and platform
5. WHEN affiliate commission is due THEN the system SHALL deduct it from platform commission, not expert commission
6. WHEN revenue distribution occurs THEN the system SHALL record each allocation separately for transparency
7. WHEN admin reviews revenue reports THEN the system SHALL show breakdown by revenue stream and recipient type

### Requirement 11: Security & Data Protection

**User Story:** As a user, I want my data and conversations to be secure and private, so that I can trust the platform with sensitive information.

#### Acceptance Criteria

1. WHEN users authenticate THEN the system SHALL use secure JWT tokens with appropriate expiration
2. WHEN sensitive data is stored THEN the system SHALL encrypt passwords and protect personal information
3. WHEN API requests are made THEN the system SHALL validate input to prevent injection attacks
4. WHEN file uploads occur THEN the system SHALL scan for malicious content and validate file types
5. WHEN CORS requests are made THEN the system SHALL only allow requests from authorized origins
6. WHEN errors are logged THEN the system SHALL not include sensitive user data in log files

### Requirement 12: Rating & Review System

**User Story:** As a user, I want to rate and review AI experts after using them, so that I can share my experience and help other users make informed decisions.

#### Acceptance Criteria

1. WHEN a user has exchanged at least 3 messages with an expert THEN the system SHALL show option to rate and review the expert
2. WHEN a user ends a chat session (closes chat or navigates away) THEN the system SHALL optionally prompt them to rate and review if they haven't already
3. WHEN a user submits a rating THEN the system SHALL accept ratings from 1 to 5 stars
4. WHEN a user submits a review THEN the system SHALL validate the review text and store it with timestamp
5. WHEN a user views an expert in marketplace THEN the system SHALL display average rating and recent reviews
6. WHEN calculating average rating THEN the system SHALL use all ratings for that expert and update in real-time
7. WHEN displaying reviews THEN the system SHALL show reviewer name, rating, review text, and date
8. WHEN a user has already reviewed an expert THEN the system SHALL allow them to update their existing review
9. WHEN expert creator views their expert stats THEN the system SHALL show detailed rating breakdown and all reviews
10. WHEN filtering experts in marketplace THEN the system SHALL allow sorting by highest rated or most reviewed
11. WHEN a review contains inappropriate content THEN the system SHALL provide reporting mechanism for moderation

### Requirement 12.1: Review Quality & Moderation

**User Story:** As a platform user, I want to see authentic and helpful reviews, so that I can trust the rating system and make good decisions.

#### Acceptance Criteria

1. WHEN a user submits a review THEN the system SHALL require minimum chat interaction before allowing review submission
2. WHEN detecting spam or fake reviews THEN the system SHALL flag them for admin review
3. WHEN admin moderates reviews THEN the system SHALL allow hiding, editing, or removing inappropriate reviews
4. WHEN a review is moderated THEN the system SHALL notify the reviewer and update the expert's rating accordingly
5. WHEN users report a review THEN the system SHALL queue it for admin moderation
6. WHEN displaying reviews THEN the system SHALL prioritize verified reviews from users with substantial platform activity

### Requirement 13: AI Recommendation System Integration

**User Story:** As a user, I want to receive personalized expert recommendations from the dedicated recommendation service, so that I can discover relevant experts based on advanced machine learning algorithms.

#### Acceptance Criteria

1. WHEN a user views the marketplace THEN the main platform SHALL request recommendations from the separate recommender service
2. WHEN the recommender service calculates recommendations THEN it SHALL use collaborative filtering, content-based filtering, and trending analysis
3. WHEN the main platform receives recommendations THEN it SHALL display them in a "Recommended for You" section with explanations
4. WHEN user interactions occur THEN the main platform SHALL send interaction data to the recommender service via API
5. WHEN the recommender service processes new data THEN it SHALL update user and expert similarity matrices using Redis caching
6. WHEN displaying recommendations THEN the system SHALL show recommendation reasons and confidence scores
7. WHEN user dismisses a recommendation THEN the system SHALL send feedback to the recommender service to improve future suggestions
8. WHEN new experts are created THEN the main platform SHALL notify the recommender service to include them in algorithms
9. WHEN the recommender service is unavailable THEN the main platform SHALL fall back to simple popularity-based recommendations
10. WHEN user preferences change THEN the recommender service SHALL adapt recommendations using real-time learning algorithms
11. WHEN the recommender service calculates similarities THEN it SHALL use batch processing with cron jobs for performance optimization
12. WHEN serving recommendations THEN the recommender service SHALL use Redis caching to ensure sub-second response times

### Requirement 14: Admin & Analytics Dashboard

**User Story:** As a platform administrator, I want comprehensive analytics and management tools, so that I can monitor platform health and make data-driven decisions.

#### Acceptance Criteria

1. WHEN admin accesses dashboard THEN the system SHALL show key metrics: user count, expert count, transaction volume
2. WHEN admin views revenue reports THEN the system SHALL display platform earnings, expert commissions, and affiliate payouts
3. WHEN admin monitors system health THEN the system SHALL show API response times, error rates, and database performance
4. WHEN admin manages users THEN the system SHALL allow viewing user details, balance adjustments, and account status changes
5. WHEN admin reviews experts THEN the system SHALL show expert performance metrics, usage statistics, and quality indicators
6. WHEN admin configures platform settings THEN the system SHALL allow updating commission rates, bonus amounts, and system parameters
7. WHEN admin monitors recommendation system THEN the system SHALL show recommender service health, cache hit rates, and algorithm performance
8. WHEN admin views recommendation analytics THEN the system SHALL display recommendation accuracy metrics, user engagement with recommendations, and trending analysis
9. WHEN admin manages recommendation settings THEN the system SHALL allow configuring algorithm weights, similarity thresholds, and cache TTL values

### Requirement 15: Microservice Architecture & Integration

**User Story:** As a system architect, I want the platform to integrate seamlessly with the separate recommender service, so that we can maintain scalable and maintainable microservice architecture.

#### Acceptance Criteria

1. WHEN the main platform needs recommendations THEN it SHALL communicate with the recommender service via REST API with proper authentication
2. WHEN user interactions occur THEN the main platform SHALL send interaction data to the recommender service asynchronously
3. WHEN the recommender service is updated THEN it SHALL maintain backward compatibility with the main platform API
4. WHEN either service experiences downtime THEN the other service SHALL continue operating with graceful degradation
5. WHEN data synchronization is needed THEN the services SHALL use event-driven communication patterns
6. WHEN scaling is required THEN each service SHALL be able to scale independently based on load
7. WHEN monitoring is needed THEN both services SHALL provide health check endpoints and metrics
8. WHEN deploying updates THEN each service SHALL support zero-downtime deployment strategies