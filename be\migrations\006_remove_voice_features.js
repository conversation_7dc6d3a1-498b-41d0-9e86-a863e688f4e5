/**
 * Migration: Remove Voice Features
 * 
 * This migration removes all voice-related features from the database:
 * - Remove voice_enabled column from experts table
 * - Remove voice-related entries from ai_generation_logs
 * - Remove voice message types from chat_messages
 * - Remove voice_duration column from chat_messages
 * 
 * Task 6.3: Database Voice Cleanup
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function up() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });

  try {
    await connection.beginTransaction();
    
    console.log('🧹 Starting voice features cleanup...');
    
    // 1. Remove voice-related entries from ai_generation_logs
    console.log('Removing voice-related entries from ai_generation_logs...');
    const [voiceLogsResult] = await connection.execute(`
      DELETE FROM ai_generation_logs 
      WHERE generation_type IN ('voice_tts', 'voice_stt')
    `);
    console.log(`✅ Removed ${voiceLogsResult.affectedRows} voice-related generation logs`);
    
    // 2. Update chat_messages to remove voice message types and voice_duration
    console.log('Updating chat_messages to remove voice message types...');
    const [voiceMessagesResult] = await connection.execute(`
      UPDATE chat_messages 
      SET message_type = 'text' 
      WHERE message_type = 'voice'
    `);
    console.log(`✅ Updated ${voiceMessagesResult.affectedRows} voice messages to text type`);
    
    // 3. Remove voice_duration column from chat_messages
    console.log('Removing voice_duration column from chat_messages...');
    const [chatMessagesColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_messages' 
      AND COLUMN_NAME = 'voice_duration'
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (chatMessagesColumns.length > 0) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        DROP COLUMN voice_duration
      `);
      console.log('✅ Removed voice_duration column from chat_messages');
    } else {
      console.log('ℹ️  voice_duration column not found in chat_messages');
    }
    
    // 4. Update message_type enum to remove 'voice' option
    console.log('Updating message_type enum to remove voice option...');
    await connection.execute(`
      ALTER TABLE chat_messages 
      MODIFY COLUMN message_type ENUM('text', 'image') DEFAULT 'text'
    `);
    console.log('✅ Updated message_type enum to remove voice option');
    
    // 5. Remove voice_enabled column from experts table
    console.log('Removing voice_enabled column from experts table...');
    const [expertsColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'experts' 
      AND COLUMN_NAME = 'voice_enabled'
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (expertsColumns.length > 0) {
      await connection.execute(`
        ALTER TABLE experts 
        DROP COLUMN voice_enabled
      `);
      console.log('✅ Removed voice_enabled column from experts');
    } else {
      console.log('ℹ️  voice_enabled column not found in experts');
    }
    
    // 6. Remove voice_enabled index if it exists
    console.log('Removing voice_enabled index if it exists...');
    try {
      await connection.execute(`
        DROP INDEX idx_experts_voice_enabled ON experts
      `);
      console.log('✅ Removed idx_experts_voice_enabled index');
    } catch (error) {
      if (error.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
        console.log('ℹ️  idx_experts_voice_enabled index not found');
      } else {
        throw error;
      }
    }
    
    await connection.commit();
    console.log('✅ Voice features cleanup completed successfully!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Voice features cleanup failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });

  try {
    await connection.beginTransaction();
    
    console.log('🔄 Reverting voice features cleanup...');
    
    // 1. Add back voice_enabled column to experts table
    console.log('Adding back voice_enabled column to experts table...');
    await connection.execute(`
      ALTER TABLE experts 
      ADD COLUMN voice_enabled TINYINT(1) DEFAULT 0 AFTER first_message
    `);
    console.log('✅ Added back voice_enabled column');
    
    // 2. Add back voice_enabled index
    console.log('Adding back voice_enabled index...');
    await connection.execute(`
      CREATE INDEX idx_experts_voice_enabled ON experts (voice_enabled)
    `);
    console.log('✅ Added back idx_experts_voice_enabled index');
    
    // 3. Update message_type enum to include 'voice' option
    console.log('Updating message_type enum to include voice option...');
    await connection.execute(`
      ALTER TABLE chat_messages 
      MODIFY COLUMN message_type ENUM('text', 'image', 'voice') DEFAULT 'text'
    `);
    console.log('✅ Updated message_type enum to include voice option');
    
    // 4. Add back voice_duration column to chat_messages
    console.log('Adding back voice_duration column to chat_messages...');
    await connection.execute(`
      ALTER TABLE chat_messages 
      ADD COLUMN voice_duration INT DEFAULT NULL COMMENT 'Voice duration in seconds' AFTER file_url
    `);
    console.log('✅ Added back voice_duration column');
    
    await connection.commit();
    console.log('✅ Voice features cleanup reverted successfully!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Voice features cleanup revert failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'up') {
    up().catch(console.error);
  } else if (command === 'down') {
    down().catch(console.error);
  } else {
    console.log('Usage: node 006_remove_voice_features.js [up|down]');
    process.exit(1);
  }
}

module.exports = { up, down };
