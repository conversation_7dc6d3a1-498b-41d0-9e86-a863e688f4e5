"use client";

import React, { useState } from "react";
import C<PERSON><PERSON>x<PERSON> from "./CreateExpert";
import <PERSON>Ex<PERSON> from "./EditExpert";
import ExpertList from "./ExpertList";
import ExpertOverview from "./ExpertOverview";
import { X } from "lucide-react";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface ExpertPanelProps {
  view?: "overview" | "manage";
}

const ExpertPanel: React.FC<ExpertPanelProps> = ({ view = "overview" }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedExpert, setSelectedExpert] = useState<Expert | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleExpertCreated = (expert: any) => {
    console.log("Expert created:", expert);
    setRefreshTrigger((prev) => prev + 1);
    setShowCreateModal(false);
  };

  const handleExpertUpdated = (expert: any) => {
    console.log("Expert updated:", expert);
    setRefreshTrigger((prev) => prev + 1);
    setShowEditModal(false);
    setSelectedExpert(null);
  };

  const handleExpertSelect = (expert: Expert) => {
    console.log("Expert selected:", expert);
    // You can add navigation or modal logic here
  };

  const handleExpertEdit = (expert: Expert) => {
    console.log("Expert edit:", expert);
    setSelectedExpert(expert);
    setShowEditModal(true);
  };

  const handleCancelEdit = () => {
    setSelectedExpert(null);
    setShowEditModal(false);
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Content based on view prop */}
      {view === "overview" ? (
        <ExpertOverview />
      ) : (
        <ExpertList
          onExpertSelect={handleExpertSelect}
          onExpertEdit={handleExpertEdit}
          refreshTrigger={refreshTrigger}
          onCreateExpert={() => setShowCreateModal(true)}
        />
      )}

      {/* Create Expert Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">
                Create New Expert
              </h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <CreateExpert
                onExpertCreated={handleExpertCreated}
                onCancel={() => setShowCreateModal(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Edit Expert Modal */}
      {showEditModal && selectedExpert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">
                Edit Expert: {selectedExpert.name}
              </h2>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <EditExpert
                expert={selectedExpert}
                onExpertUpdated={handleExpertUpdated}
                onCancel={handleCancelEdit}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExpertPanel;
