"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Share2 } from "lucide-react";
import { api } from "@/lib/api";
import { asset } from "@/lib/utils";
import StarRating from "@/components/ui/star-rating";
import { Button } from "@/components/ui/button";
import ReviewModal from "./ReviewModal";
import RatingSummary from "./RatingSummary";
import ReviewList from "./ReviewList";
import ShareCreator from "@/components/sharing/ShareCreator";
import { SimilarExperts } from "@/components/SimilarExperts";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  totalChats?: number;
  totalRevenue?: number;
  averageRating?: number;
  totalReviews?: number;
  createdAt: string;
  updatedAt: string;
}

interface ExpertProfileProps {
  expertId: string;
}

const ExpertProfile: React.FC<ExpertProfileProps> = ({ expertId }) => {
  const [expert, setExpert] = useState<Expert | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [activeTab, setActiveTab] = useState<"about" | "reviews">("about");
  const router = useRouter();

  const loadExpert = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await api.getExpert(expertId);

      if (result.success) {
        setExpert(result.expert);
      } else {
        setError(result.error || "Failed to load expert");
      }
    } catch (err: any) {
      setError(err.message || "Failed to load expert");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadExpert();
  }, [expertId]); // eslint-disable-line react-hooks/exhaustive-deps

  const getExpertIcon = (labels: string[]) => {
    if (labels.includes("business") || labels.includes("marketing"))
      return "💼";
    if (labels.includes("code") || labels.includes("programming")) return "💻";
    if (labels.includes("creative") || labels.includes("design")) return "🎨";
    if (labels.includes("education") || labels.includes("learning"))
      return "📚";
    if (labels.includes("health") || labels.includes("medical")) return "🏥";
    if (labels.includes("finance") || labels.includes("money")) return "💰";
    return "🤖";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const startChat = () => {
    router.push(`/chat?expertId=${expertId}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Navigation */}
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <span>←</span>
              <span>Back to Marketplace</span>
            </Link>
          </div>

          {/* Loading Skeleton */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 animate-pulse">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-gray-200 rounded-full"></div>
              </div>
              <div className="flex-1 space-y-4">
                <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !expert) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <span>←</span>
              <span>Back to Marketplace</span>
            </Link>
          </div>

          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto">
              <div className="text-red-600 text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-semibold text-red-800 mb-2">
                Expert Not Found
              </h3>
              <p className="text-red-600 mb-4">
                {error ||
                  "This expert may not exist or is not publicly available."}
              </p>
              <Link
                href="/"
                className="inline-block px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Return to Marketplace
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
          >
            <span>←</span>
            <span>Back to Marketplace</span>
          </Link>
        </div>

        {/* Expert Profile */}
        <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-900 to-indigo-900 p-8 text-white">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="relative">
                {expert.imageUrl ? (
                  <Image
                    src={asset(expert.imageUrl)}
                    alt={expert.name}
                    width={128}
                    height={128}
                    className="w-32 h-32 object-cover rounded-full border-4 border-white shadow-xl"
                  />
                ) : (
                  <div className="w-32 h-32 rounded-full bg-white/20 backdrop-blur-sm border-4 border-white shadow-xl flex items-center justify-center text-6xl">
                    {getExpertIcon(expert.labels)}
                  </div>
                )}
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>

              <div className="flex-1 text-center md:text-left">
                <h1 className="text-4xl font-bold mb-2">{expert.name}</h1>
                <div className="flex flex-wrap gap-2 justify-center md:justify-start mb-4">
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium">
                    {expert.model}
                  </span>
                  <span className="px-3 py-1 bg-green-500 rounded-full text-sm font-medium">
                    ● Online
                  </span>
                </div>
                
                {/* Rating Display */}
                {expert.averageRating && expert.averageRating > 0 ? (
                  <div className="flex items-center justify-center md:justify-start gap-2">
                    <StarRating rating={expert.averageRating} size="md" />
                    <span className="text-white/90 text-sm">
                      ({expert.totalReviews} review{expert.totalReviews !== 1 ? 's' : ''})
                    </span>
                  </div>
                ) : (
                  <div className="text-white/70 text-sm text-center md:text-left">
                    No reviews yet
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-8">
            {/* Tab Navigation */}
            <div className="flex gap-4 mb-8 border-b border-gray-200">
              <button
                onClick={() => setActiveTab("about")}
                className={`pb-4 px-2 font-medium transition-colors ${
                  activeTab === "about"
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                About
              </button>
              <button
                onClick={() => setActiveTab("reviews")}
                className={`pb-4 px-2 font-medium transition-colors ${
                  activeTab === "reviews"
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Reviews ({expert.totalReviews || 0})
              </button>
            </div>

            {activeTab === "about" && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* About Section */}
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    About This Expert
                  </h2>
                  <div className="bg-gray-50 rounded-xl p-6">
                    <p className="text-gray-700 leading-relaxed mb-4">
                      {expert.description}
                    </p>
                    
                  </div>
                </div>

                {/* Expertise Areas */}
                {expert.labels && expert.labels.length > 0 && (
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">
                      Areas of Expertise
                    </h2>
                    <div className="flex flex-wrap gap-3">
                      {expert.labels.map((label, index) => (
                        <span
                          key={index}
                          className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-medium hover:bg-blue-200 transition-colors"
                        >
                          #{label}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional Info */}
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    Expert Details
                  </h2>
                  <div className="bg-gray-50 rounded-xl p-6 space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">AI Model:</span>
                      <span className="font-semibold">{expert.model}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Response Time:</span>
                      <span className="font-semibold text-green-600">
                        Instant
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Availability:</span>
                      <span className="font-semibold text-green-600">24/7</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Added:</span>
                      <span className="font-semibold">
                        {formatDate(expert.createdAt)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Action Card */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    Start Conversation
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Ready to get expert assistance? Start chatting now for
                    instant responses.
                  </p>

                  <button
                    onClick={startChat}
                    className="w-full py-4 px-6 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1 text-lg mb-4"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    🚀 Start Chat
                  </button>
                  
                  <Button
                    onClick={() => setShowShareModal(true)}
                    variant="outline"
                    className="w-full mb-3"
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Expert
                  </Button>
                  
                  <Button
                    onClick={() => setShowReviewModal(true)}
                    variant="outline"
                    className="w-full"
                  >
                    Write a Review
                  </Button>
                </div>

                {/* Features */}
                <div className="bg-white rounded-2xl p-6 border border-gray-200">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">
                    What You Get
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">✓</span>
                      </div>
                      <span className="text-gray-700">Instant responses</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Specialized knowledge
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">✓</span>
                      </div>
                      <span className="text-gray-700">24/7 availability</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 text-sm">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Secure conversations
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            )}

            {activeTab === "reviews" && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <RatingSummary expertId={expert.id} />
                  
                  <div className="flex items-center justify-center">
                    <Button
                      onClick={() => setShowReviewModal(true)}
                      size="lg"
                      className="px-8"
                    >
                      Write a Review
                    </Button>
                  </div>
                </div>
                
                <ReviewList 
                  expertId={expert.id} 
                  showPagination={true}
                  limit={10}
                />
              </div>
            )}
          </div>
        </div>

        {/* Similar Experts Section */}
        <div className="mt-12">
          <SimilarExperts
            expertId={expert.id}
            limit={6}
            title="Similar Experts"
            description="Other experts you might like"
            showRefreshButton={true}
            className="bg-gray-50 rounded-xl p-6"
          />
        </div>

        {/* Review Modal */}
        {showReviewModal && (
          <ReviewModal
            isOpen={showReviewModal}
            onClose={() => setShowReviewModal(false)}
            expertId={expert.id}
            expertName={expert.name}
            onReviewSubmitted={() => {
              // Refresh expert data to update review count
              loadExpert();
            }}
          />
        )}

        {/* Share Modal */}
        <ShareCreator
          expert={expert}
          isOpen={showShareModal}
          onOpenChange={setShowShareModal}
          showTrigger={false}
          onShareCreated={(shareData) => {
            console.log('Share created:', shareData);
            setShowShareModal(false);
          }}
          onCancel={() => setShowShareModal(false)}
        />
      </div>
    </div>
  );
};

export default ExpertProfile;
