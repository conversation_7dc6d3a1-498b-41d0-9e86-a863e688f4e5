const express = require('express');
const chatController = require('../../controllers/chatController');
const { authenticateToken } = require('../../middleware/auth');
const InteractionTracker = require('../../middleware/interactionTracker');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: Chat functionality with AI experts
 */

// Chat with AI expert
/**
 * @swagger
 * /api/chat:
 *   post:
 *     summary: Send message to AI expert
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *               expertId:
 *                 type: string
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Chat response
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid input
 */
router.post('/', InteractionTracker.trackChatStart, chatController.chat);

// Get thread messages
/**
 * @swagger
 * /api/thread/{threadId}/messages:
 *   get:
 *     summary: Get messages from a thread
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: threadId
 *         schema:
 *           type: string
 *         required: true
 *         description: Thread ID
 *     responses:
 *       200:
 *         description: Thread messages
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Thread not found
 */
router.get('/thread/:threadId/messages', chatController.getThreadMessages);



module.exports = router;
